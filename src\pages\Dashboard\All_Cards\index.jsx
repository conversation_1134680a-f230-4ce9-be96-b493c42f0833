import React, { useState, useRef, useEffect } from 'react';
import { Tooltip } from 'primereact/tooltip';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Dropdown } from 'primereact/dropdown';
import { TfiTrash } from 'react-icons/tfi';
import { FaRegEye, FaSearch } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import Container from '@components/Container';
import CreateCardForm from '../Backages/CreateCardForm';
import { useFetchCards } from "../../../quires/useGetCards ";
import { Toast } from 'primereact/toast';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { useLayout } from '../../../contexts/LayoutContext';

const CardsDataTable = () => {
  const { data: cards, isLoading, isError, error, refetch } = useFetchCards();
  const { isMobile } = useLayout();
  const [isCreateCardModalOpen, setIsCreateCardModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [assignmentFilter, setAssignmentFilter] = useState('all');
  const [filteredCards, setFilteredCards] = useState([]);
  const toast = useRef(null);
  const backendUrl = import.meta.env.VITE_BACKEND_URL;
  const token = localStorage.getItem("token");

  // Assignment filter options
  const assignmentOptions = [
    { label: 'All Cards', value: 'all' },
    { label: 'Assigned Cards', value: 'assigned' },
    { label: 'Unassigned Cards', value: 'unassigned' }
  ];

  // Filter cards based on search query and assignment status
  useEffect(() => {
    const cardData = cards?.data || [];
    let filtered = [...cardData];

    // Apply search filter
    if (searchQuery.trim()) {
      const searchLower = searchQuery.toLowerCase();
      filtered = filtered.filter(card => {
        return (
          card.name?.toLowerCase().includes(searchLower) ||
          card.number?.toLowerCase().includes(searchLower) ||
          card.manager_name?.toLowerCase().includes(searchLower) ||
          card.card_type?.name?.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply assignment filter
    if (assignmentFilter !== 'all') {
      filtered = filtered.filter(card => {
        const isAssigned = card.manager_name && card.manager_name !== 'No Manager' && card.manager_name !== '';
        return assignmentFilter === 'assigned' ? isAssigned : !isAssigned;
      });
    }

    setFilteredCards(filtered);
  }, [cards?.data, searchQuery, assignmentFilter]);

  const openCreateCardModal = () => {
    setIsEditMode(false);
    setIsCreateCardModalOpen(true);
  };

  const handleEdit = (rowData) => {
    setEditData(rowData);
    setIsEditMode(true);
    setIsCreateCardModalOpen(true);
  };

  const resetEditMode = () => {
    setIsEditMode(false);
    setEditData(null);
  };

  const handleDelete = (id) => {
    confirmDialog({
      message: 'Are you sure you want to delete this card?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => deleteCard(id),
      reject: () => {}
    });
  };

  const deleteCard = async (id) => {
    try {
      const response = await fetch(`${backendUrl}/cards/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Card deleted successfully',
          life: 3000
        });
        refetch();
      } else {
        const errorData = await response.json();
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: errorData.message || 'Failed to delete card',
          life: 3000
        });
      }
    } catch (error) {
      console.error('Error:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred while deleting card',
        life: 3000
      });
    }
  };

  const actionsBodyTemplate = (rowData) => (
    <div className="flex justify-around">
      <Tooltip target=".edit-icon" content="Edit" position="top" />
      <button className="edit-icon" onClick={() => handleEdit(rowData)}>
        <FiEdit className="text-yellow-500" size={20} />
      </button>

      <Tooltip target=".delete-icon" content="Delete" position="top" />
      <button className="delete-icon" onClick={() => handleDelete(rowData.id)}>
        <TfiTrash className="text-red-500" size={20} />
      </button>
    </div>
  );

  if (isLoading) return <p>Loading...</p>;
  if (isError) return <p>Error: {error.message}</p>;

  return (
    <Container>
      <Toast ref={toast} />
      <ConfirmDialog />
      <div className="w-full flex justify-between items-center mb-4">
        <div className="w-4/12">
          <h1 className="text-xl font-bold">Card Management</h1>
        </div>

        <div className="w-8/12 flex justify-end">
          <button
            className="main-btn text-md shadow-md"
            onClick={openCreateCardModal}
          >
            Create New Card
          </button>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className={`w-full mb-4 mt-1 ${isMobile ? 'px-2 space-y-3' : 'flex justify-center items-center gap-4'}`}>
        {/* Search Bar */}
        <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[500px]'}`}>
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            placeholder="Search by card name, number, manager, or card type..."
            className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                      focus:outline-none focus:ring-2 focus:ring-blue-300
                      focus:border-blue-300 transition-all duration-200"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Assignment Filter */}
        <div className={`${isMobile ? 'w-full' : 'w-48'}`}>
          <Dropdown
            value={assignmentFilter}
            options={assignmentOptions}
            onChange={(e) => setAssignmentFilter(e.value)}
            placeholder="Filter by Assignment"
            className="w-full border rounded-md shadow-md"
            panelClassName="shadow-lg"
          />
        </div>
      </div>

      <CreateCardForm
        isModalOpen={isCreateCardModalOpen}
        setIsModalOpen={setIsCreateCardModalOpen}
        fetchCards={refetch}
        editData={editData}
        isEditMode={isEditMode}
        resetEditMode={resetEditMode}
        lazy
        filterDisplay="row"
        responsiveLayout="stack"
        breakpoint="960px"
        dataKey="id"
        paginator
        className="table border"

        rowsPerPageOptions={[5, 25, 50, 100]}

        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"

        scrollable
        scrollHeight="calc(100vh - 400px)"
      />

      <DataTable value={filteredCards} paginator rows={10} className="mt-4"
                        scrollable
                  scrollHeight="100%"
      >
        <Column field="name" header="Card Name" />
        <Column field="number" header="Card Number" />
        <Column field="card_type.type_of_connection" header="Card Type of Connection" />
        <Column field="card_type.name" header="Card Type" />
        <Column
          field="manager_name"
          header="Manager Name"
          className="text-left"
          body={(rowData) => (
            <div className="flex items-center gap-2">
              <span
                className={`inline-block w-2 h-2 rounded-full ${
                  rowData.manager_name && rowData.manager_name !== 'No Manager' && rowData.manager_name !== ''
                    ? 'bg-green-500'
                    : 'bg-gray-400'
                }`}
                title={rowData.manager_name && rowData.manager_name !== 'No Manager' && rowData.manager_name !== '' ? 'Assigned' : 'Unassigned'}
              />
              <span>{rowData.manager_name || 'Unassigned'}</span>
            </div>
          )}
        />

        <Column body={actionsBodyTemplate} header="Actions" />
      </DataTable>
    </Container>
  );
};

export default CardsDataTable;