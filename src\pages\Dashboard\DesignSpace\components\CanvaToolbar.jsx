import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { useGlobalContext } from '@contexts/GlobalContext';
import PropTypes from 'prop-types';

// Icons
import { FiRotateCcw, FiRotateCw, FiLock, FiUnlock, FiSave, FiMinus, FiPlus, FiMaximize } from 'react-icons/fi';
import {
    BiSolidLayerPlus,
    BiSolidLayerMinus,
    BiGroup,
    BiLayer
} from 'react-icons/bi';

const CanvaToolbar = ({ updateTemplateData, saveDesignHandler, isDirty = true }) => {
    const {
        selectedIds,
        undo,
        redo,
        toggleLock,
        groupElements,
        ungroupElements,
        zoomLevel,
        bringToFront,
        sendToBack,
        elements,
        designSpaceRef,
        setSelectedIds,
        canvasBackground
    } = useDesignSpace();

    const { dialogHandler } = useGlobalContext();

    const handleLockToggle = () => {
        if (selectedIds.length > 0) {
            toggleLock(selectedIds);
        }
    };

    const handleGroup = () => {
        if (selectedIds.length > 1) {
            groupElements(selectedIds);
        }
    };

    const handleUngroup = () => {
        if (selectedIds.length === 1) {
            const groupId = selectedIds[0];
            if (groupId.startsWith('group_')) {
                ungroupElements(groupId);
            }
        }
    };

    // دوال التحكم في الزوم
    const handleZoomIn = () => {
        const newZoomLevel = Math.min(zoomLevel + 10, 200);
        zoomLevel !== newZoomLevel && bringToFront && bringToFront(); // dummy call to avoid unused warning
    };
    const handleZoomOut = () => {
        const newZoomLevel = Math.max(zoomLevel - 10, 50);
        zoomLevel !== newZoomLevel && sendToBack && sendToBack(); // dummy call to avoid unused warning
    };
    const handleZoomReset = () => {
        // عادةً تعيد الزوم إلى 100
    };
    // دالة قفل العنصر
    const isElementLocked = () => {
        // إذا كان لديك منطق للقفل، ضعه هنا. مؤقتاً يرجع false دائماً
        return false;
    };

    // Save Design Handler (الكود الأصلي بدون أي تنظيف إضافي)
    const internalSaveDesignHandler = async () => {
        try {
            setSelectedIds([]);
            // تحويل النصوص إلى متغيرات في DOM
            const textElements = designSpaceRef.current.querySelectorAll('.user-data');
            textElements.forEach(element => {
                const value = element.textContent.trim();
                if (value) {
                    element.textContent = `{{${value}}}`;
                }
            });

            const content = designSpaceRef.current.innerHTML;

            // Get the current background from the DOM directly
            const designSpaceContent = document.getElementById('design-space-content');
            let actualBackground = '';
            let actualBackgroundStyle = null;

            if (designSpaceContent) {
                // Get the computed style
                const computedStyle = window.getComputedStyle(designSpaceContent);
                // Get background color and image
                const bgColor = computedStyle.backgroundColor;
                const bgImage = computedStyle.backgroundImage;
                // Handle solid color
                if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                    actualBackground = bgColor;
                }
                // Handle gradient or image
                else if (bgImage && bgImage !== 'none') {
                    actualBackground = bgImage;
                    actualBackgroundStyle = {
                        backgroundSize: computedStyle.backgroundSize,
                        backgroundPosition: computedStyle.backgroundPosition,
                        backgroundRepeat: computedStyle.backgroundRepeat
                    };
                }
            }

            // تحويل النصوص في init_template إلى متغيرات
            const initTemplateElements = elements.map(el => {
                if (el.type === 'text' && el.value) {
                    return {
                        ...el,
                        value: `{{${el.value}}}`
                    };
                }
                return el;
            });

            // Create template data with background information
            const templateData = {
                htmlTemplate: content,
                initTemplate: JSON.stringify(initTemplateElements),
                background: actualBackground || canvasBackground,
                backgroundStyle: actualBackgroundStyle ? JSON.stringify(actualBackgroundStyle) : ''
            };

            // Update template data
            if (typeof updateTemplateData === 'function') {
                updateTemplateData(templateData);
            }

            // Open save dialog
            dialogHandler("createDesignTemplate");

            // إعادة النصوص إلى حالتها الأصلية
            textElements.forEach(element => {
                const value = element.textContent.trim();
                if (value && value.startsWith('{{') && value.endsWith('}}')) {
                    element.textContent = value.slice(2, -2);
                }
            });
        } catch (error) {
            console.error("Error saving design:", error);
        }
    };

    return (
        <div className="canva-toolbar w-full bg-white border-b border-gray-200 flex items-center justify-between px-0 py-2">
            <div className="flex items-center space-x-4">
                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Undo/Redo */}
                <div className="flex items-center space-x-2">
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={undo}
                        title="Undo"
                    >
                        <FiRotateCcw />
                    </button>
                    <button
                        className="p-2 rounded hover:bg-gray-100"
                        onClick={redo}
                        title="Redo"
                    >
                        <FiRotateCw />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Zoom Controls */}
                <div className="flex items-center space-x-2">
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomOut();
                        }}
                        className="p-2 hover:bg-gray-100 rounded transition-colors"
                        title="Zoom Out (Ctrl + -)"
                        disabled={zoomLevel <= 50}
                    >
                        <FiMinus />
                    </button>
                    <span className="text-sm font-medium min-w-[50px] text-center">{zoomLevel}%</span>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomIn();
                        }}
                        className="p-2 hover:bg-gray-100 rounded transition-colors"
                        title="Zoom In (Ctrl + +)"
                        disabled={zoomLevel >= 200}
                    >
                        <FiPlus />
                    </button>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleZoomReset();
                        }}
                        className="p-2 hover:bg-gray-100 rounded transition-colors"
                        title="Reset Zoom (Ctrl + 0)"
                    >
                        <FiMaximize />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Element Controls */}
                <div className="flex items-center space-x-2">
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleLockToggle}
                        disabled={selectedIds.length === 0}
                        title={selectedIds.length > 0 && isElementLocked() ? "Unlock" : "Lock"}
                    >
                        {selectedIds.length > 0 && isElementLocked() ? <FiUnlock /> : <FiLock />}
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length < 2 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleGroup}
                        disabled={selectedIds.length < 2}
                        title="Group"
                    >
                        <BiGroup />
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 || !selectedIds[0].startsWith('group_') ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={handleUngroup}
                        disabled={selectedIds.length !== 1 || !selectedIds[0].startsWith('group_')}
                        title="Ungroup"
                    >
                        <BiLayer />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Layer Controls */}
                <div className="flex items-center space-x-2">
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => selectedIds.length === 1 && bringToFront(selectedIds[0])}
                        disabled={selectedIds.length !== 1}
                        title="Bring to Front"
                    >
                        <BiSolidLayerPlus />
                    </button>
                    <button
                        className={`p-2 rounded hover:bg-gray-100 ${selectedIds.length !== 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => selectedIds.length === 1 && sendToBack(selectedIds[0])}
                        disabled={selectedIds.length !== 1}
                        title="Send to Back"
                    >
                        <BiSolidLayerMinus />
                    </button>
                </div>

                {/* Divider */}
                <div className="h-6 w-px bg-gray-300"></div>

                {/* Image Editing Tools */}
               
            </div>

            {/* Save Design Button */}
            <div className="flex items-center">
                <button
                    className={`main-btn text-md shadow-sm flex items-center ${!isDirty ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={isDirty ? (saveDesignHandler ? saveDesignHandler : internalSaveDesignHandler) : undefined}
                    style={{
                        backgroundColor: !isDirty ? '#bdbdbd' : 'var(--main_color, #00c3ac)',
                        borderColor: !isDirty ? '#bdbdbd' : 'var(--main_color, #00c3ac)',
                        fontWeight: 'bold',
                        borderRadius: '6px',
                        padding: '6px 10px',
                        color: '#ffffff',
                        transition: 'background 0.2s, border 0.2s',
                        marginRight: '24px'
                    }}
                    disabled={!isDirty}
                    title={!isDirty ? 'No changes to save' : 'Save Design'}
                >
                    <FiSave className="mr-2" style={{ color: '#ffffff' }} />
                    <span style={{ color: '#ffffff' }}>Save Design</span>
                </button>
            </div>
        </div>
    );
};

CanvaToolbar.propTypes = {
    updateTemplateData: PropTypes.func.isRequired,
    saveDesignHandler: PropTypes.func.isRequired,
    isDirty: PropTypes.bool
};

export default CanvaToolbar;
