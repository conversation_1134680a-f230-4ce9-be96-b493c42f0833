import React, { useRef } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Toast } from 'primereact/toast';
import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import SideImage from './SideImage';
import { useForgetPasswordMutation } from '../../quires';
import { getFormErrorMessage } from '@utils/helper';

function ForgotPassword() {
  const { t } = useTranslation("auth");
  const { formState: { errors }, handleSubmit, control, reset } = useForm();
  const forgetPassword = useForgetPasswordMutation();
  const toast = useRef(null);

  const onSubmit = async (data) => {
    try {
      await forgetPassword.mutateAsync(data);
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Password reset instructions have been sent to your email.',
        life: 5000
      });
      reset(); // Clear the form after successful submission
    } catch (error) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: error.response?.data?.message || 'Failed to send reset instructions. Please try again.',
        life: 5000
      });
    }
  };

  return (
    <div className='w-full h-[100vh] overflow-hidden flex'>
      <Toast ref={toast} />
      <SideImage />
      <div className='w-full sm:w-7/12 h-full px-6 md:px-12 flex flex-col justify-center'>
        <div className="max-w-md mx-auto w-full">
          <h1 className='text-3xl font-bold mb-4'>Forgot Password</h1>
          <p className='text-[#696F79] mb-8 text-sm leading-relaxed'>
            Enter your email address and we'll send you instructions to reset your password.
          </p>
          
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
            {/* Email Field */}
            <div className="mb-6 w-full">
              <div className="field">
                <label className="form-label mb-2 text-[#696F79]">Email Address</label>
                <span className="p-float-label mt-2">
                  <Controller 
                    name="email" 
                    control={control}
                    rules={{
                      required: 'Email is required',
                      pattern: {
                        value: /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
                        message: 'Please enter a valid email address',
                      }
                    }}
                    render={({ field, fieldState }) => (
                      <InputText
                        id={field.name}
                        {...field}
                        inputRef={field.ref}
                        placeholder="Enter your email address"
                        className={`w-full text-[#696F79] p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                        disabled={forgetPassword.isLoading}
                      />
                    )}
                  />
                </span>
                {getFormErrorMessage('email', errors)}
              </div>
            </div>

            <button 
              type="submit"
              className="main-btn w-full mt-4 text-md sm:text-xl"
              disabled={forgetPassword.isLoading}
            >
              {forgetPassword.isLoading ? 'Sending...' : 'Send Reset Instructions'}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-[#696F79] text-sm">
              Remember your password?
              <Link to="/login">
                <span className="mx-1 capitalize text-[#427bf0] hover:underline">Back to Login</span>
              </Link>
            </p>
          </div>

          <div className="mt-4 text-center">
            <p className="text-[#696F79] text-sm">
              Don't have an account?
              <Link to="/register">
                <span className="mx-1 capitalize text-[#427bf0] hover:underline">Sign up</span>
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ForgotPassword;
